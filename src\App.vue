<script setup lang="ts">
import { RouterView } from 'vue-router'
</script>

<template>
  <RouterView />
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  background-color: #f5f5f5;
  overflow: hidden;
}

#app {
  height: 100vh;
  width: 100vw;
}

/* 图标字体类 - 临时使用文字代替 */
.icon-robot::before {
  content: '🤖';
}

.icon-chat::before {
  content: '💬';
}

.icon-history::before {
  content: '📝';
}

.icon-template::before {
  content: '📋';
}

.icon-knowledge::before {
  content: '📚';
}

.icon-settings::before {
  content: '⚙️';
}

.icon-user::before {
  content: '👤';
}

.icon-plus::before {
  content: '+';
}

.icon-arrow-left::before {
  content: '←';
}

.icon-arrow-right::before {
  content: '→';
}

.icon-clear::before {
  content: '🗑️';
}

.icon-send::before {
  content: '📤';
}

.icon-search::before {
  content: '🔍';
}

.icon-feature::before {
  content: '🔧';
}

.icon-code::before {
  content: '💻';
}

.icon-edit::before {
  content: '✏️';
}

.icon-study::before {
  content: '📖';
}

.icon-translate::before {
  content: '🌐';
}

.icon-book::before {
  content: '📚';
}

.icon-lightbulb::before {
  content: '💡';
}

.icon-chart::before {
  content: '📊';
}
</style>
