<script setup lang="ts">
import { RouterView } from 'vue-router'
</script>

<template>
  <RouterView />
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  background-color: #f5f5f5;
  overflow: hidden;
}

#app {
  height: 100vh;
  width: 100vw;
}

/* 添加到 App.vue 或全局样式文件 */
.chat-layout {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
  transition: all 0.3s ease;
}

/* 中间面板显示时的布局 */
.chat-layout.center-visible .center-panel {
  width: 320px;
}

.chat-layout.center-visible .chat-panel {
  flex: 1;
}

/* 中间面板隐藏时的布局 */
.chat-layout.center-hidden .center-panel {
  width: 0;
  overflow: hidden;
}

.chat-layout.center-hidden .chat-panel {
  flex: 1;
  margin-left: 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .center-panel {
    width: 280px !important;
  }
}

@media (max-width: 768px) {
  .chat-layout {
    flex-direction: column;
  }

  .left-sidebar {
    width: 100%;
    height: 60px;
    flex-direction: row;
  }

  .sidebar-nav .nav-list {
    display: flex;
  }

  .center-panel {
    position: absolute;
    top: 60px;
    left: 0;
    width: 100% !important;
    height: calc(100vh - 60px);
    z-index: 1000;
  }

  .chat-panel {
    margin-top: 60px;
    height: calc(100vh - 60px);
  }
}
</style>
