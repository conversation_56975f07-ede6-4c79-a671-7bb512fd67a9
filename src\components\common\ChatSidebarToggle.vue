<template>
    <div class="chat-sidebar-toggle" :class="{ 'collapsed': !isExpanded }" @click="$emit('toggle')"
        :title="isExpanded ? '收起聊天列表' : '展开聊天列表'">
        <el-icon size="15">
            <ArrowLeftBold v-if="isExpanded" />
            <ArrowRightBold v-else />
        </el-icon>
    </div>
</template>

<script setup lang="ts">
import { ArrowLeftBold, ArrowRightBold } from '@element-plus/icons-vue'
defineProps<{
    isExpanded: boolean;
}>();

defineEmits<{
    toggle: [];
}>();
</script>

<style scoped>
.chat-sidebar-toggle {
    position: absolute;
    right: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 48px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chat-sidebar-toggle:hover {
    background: #f5f5f5;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #1890ff;
}

.chat-sidebar-toggle i {
    font-size: 12px;
    color: #666;
    transition: color 0.3s ease;
}

.chat-sidebar-toggle:hover i {
    color: #1890ff;
}

.chat-sidebar-toggle.collapsed {
    right: -12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-sidebar-toggle {
        display: none;
        /* 在移动端隐藏切换按钮 */
    }
}
</style>
