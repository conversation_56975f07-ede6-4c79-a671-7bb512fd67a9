<!-- components/common/ToggleButton.vue -->
<template>
    <div class="toggle-button" :class="{ 'collapsed': !isExpanded }" @click="$emit('toggle')"
        :title="isExpanded ? '收起侧边栏' : '展开侧边栏'">
        <i :class="isExpanded ? 'icon-arrow-left' : 'icon-arrow-right'"></i>
    </div>
</template>

<script setup lang="ts">
defineProps<{
    isExpanded: boolean;
}>();

defineEmits<{
    toggle: [];
}>();
</script>

<style scoped>
.toggle-button {
    position: absolute;
    left: calc(240px + 320px - 12px);
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 48px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toggle-button:hover {
    background: #f5f5f5;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toggle-button.collapsed {
    left: calc(240px - 12px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .toggle-button {
        display: none;
        /* 在移动端隐藏收缩按钮 */
    }
}
</style>