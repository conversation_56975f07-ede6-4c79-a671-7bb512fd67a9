<!-- components/layout/CenterPanel.vue -->
<template>
    <div class="center-panel">
        <!-- 面板头部 -->
        <div class="panel-header">
            <h3>{{ panelTitle }}</h3>
            <div class="header-actions">
                <el-button v-if="layoutStore.activeNav === 'chat'" type="primary" size="small" @click="handleNewChat">
                    <i class="icon-plus"></i>
                    新建对话
                </el-button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="panel-content">
            <template v-if="layoutStore.activeNav === 'chat'">
                <ChatManagement />
            </template>

            <template v-else-if="layoutStore.activeNav === 'history'">
            </template>

            <template v-else>
                <div class="empty-state">
                    <i class="icon-feature"></i>
                    <p>功能开发中</p>
                </div>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useLayoutStore } from '@/stores/layout';
import { computed } from 'vue';
import ChatManagement from './ChatManagement.vue';

const layoutStore = useLayoutStore();

const panelTitle = computed(() => {
    const titles: Record<string, string> = {
        chat: '对话管理',
        history: '历史记录',
        templates: '对话模板',
        knowledge: '知识库',
        settings: '系统设置'
    };
    return titles[layoutStore.activeNav] || '功能面板';
});

const handleNewChat = (): void => {
    const newChat = {
        title: '新对话',
        preview: '开始新的对话...',
        time: new Date().toLocaleString('zh-CN')
    };
    layoutStore.addChatHistory(newChat);
};
</script>

<style scoped>
.center-panel {
    width: 320px;
    background: white;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    height: 100vh;
    flex-shrink: 0;
    transition: width 0.3s ease;
}

.panel-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.header-actions {
    cursor: pointer;
}

.panel-content {
    flex: 1;
    overflow-y: auto;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #999;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .center-panel {
        position: absolute;
        left: 60px;
        z-index: 1000;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }
}
</style>