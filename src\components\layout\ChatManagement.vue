<!-- components/layout/ChatManagement.vue -->
<template>
    <div class="chat-management">
        <div class="search-box">
            <el-input v-model="searchKeyword" placeholder="搜索对话..." prefix-icon="icon-search" size="small" />
        </div>
        <div class="chat-list">
            <div v-for="chat in filteredChats" :key="chat.id"
                :class="['chat-item', { active: layoutStore.currentChat?.id === chat.id }]"
                @click="layoutStore.setCurrentChat(chat)">
                <div class="chat-title">
                    {{ chat.title }}
                    <span v-if="chat.unread" class="unread-dot"></span>
                </div>
                <div class="chat-preview">{{ chat.preview }}</div>
                <div class="chat-time">{{ formatTime(chat.time) }}</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useLayoutStore } from '@/stores/layout';
import { computed, ref } from 'vue';
import type { ChatHistory } from '@/types/layout';

const layoutStore = useLayoutStore();
const searchKeyword = ref<string>('');

const filteredChats = computed(() => {
    if (!searchKeyword.value) {
        return layoutStore.chatHistory;
    }
    return layoutStore.chatHistory.filter(chat =>
        chat.title.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        chat.preview.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
});

const formatTime = (time: string): string => {
    const date = new Date(time);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) {
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (days === 1) {
        return '昨天';
    } else if (days < 7) {
        return `${days}天前`;
    } else {
        return date.toLocaleDateString('zh-CN');
    }
};
</script>

<style scoped>
.chat-management {
    padding: 10px;
}

.search-box {
    padding: 10px;
}

.chat-list {
    padding: 10px 0;
}

.chat-item {
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border: 1px solid #f0f0f0;
}

.chat-item:hover {
    background-color: #f8f9fa;
    border-color: #e0e0e0;
}

.chat-item.active {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

.chat-title {
    font-weight: 600;
    margin-bottom: 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.unread-dot {
    width: 8px;
    height: 8px;
    background: #f44336;
    border-radius: 50%;
}

.chat-preview {
    color: #666;
    font-size: 12px;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chat-time {
    color: #999;
    font-size: 11px;
}
</style>