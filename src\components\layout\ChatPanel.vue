<!-- components/layout/ChatPanel.vue -->
<template>
    <div class="chat-panel">
        <!-- 聊天头部 -->
        <!-- <div class="chat-header">
            <div class="chat-info">
                <h3>{{ currentChatTitle }}</h3>
                <span class="chat-status">AI助手在线</span>
            </div>
            <div class="chat-actions">
                <el-button type="text" size="small" @click="layoutStore.clearCurrentChat">
                    <i class="icon-clear"></i>
                </el-button>
            </div>
        </div> -->

        <!-- 聊天消息区域 -->
        <div class="chat-messages" ref="messagesContainer">
            <div v-for="message in layoutStore.messages" :key="message.id" :class="['message', message.type]">
                <div class="message-avatar">
                    <el-avatar :size="32">
                        <i :class="message.type === 'user' ? 'icon-user' : 'icon-robot'"></i>
                    </el-avatar>
                </div>
                <div class="message-content">
                    <div class="message-bubble">
                        {{ message.content }}
                    </div>
                    <div class="message-time">
                        {{ message.time }}
                    </div>
                </div>
            </div>
        </div>

        <!-- 聊天输入区域 -->
        <div class="chat-input-area">
            <div class="input-container">
                <el-input v-model="inputMessage" type="textarea" :rows="3" placeholder="输入您的问题..." resize="none"
                    @keydown.enter="handleKeydown" />
                <el-button type="primary" class="send-btn" :disabled="!inputMessage.trim()" @click="sendMessage">
                    <i class="icon-send"></i>
                </el-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useLayoutStore } from '@/stores/layout';
import { ref, computed, nextTick, onMounted, watch } from 'vue';

const layoutStore = useLayoutStore();
const messagesContainer = ref<HTMLElement>();
const inputMessage = ref<string>('');

const currentChatTitle = computed(() =>
    layoutStore.currentChat?.title || '新对话'
);

const sendMessage = async (): Promise<void> => {
    if (!inputMessage.value.trim()) return;

    const userMessage = inputMessage.value.trim();
    inputMessage.value = '';

    // 添加用户消息
    layoutStore.addMessage({
        type: 'user',
        content: userMessage,
        time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    });

    scrollToBottom();

    // 模拟AI回复
    setTimeout(() => {
        layoutStore.addMessage({
            type: 'assistant',
            content: `您说: "${userMessage}" - 这是AI的模拟回复。`,
            time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
        });
        scrollToBottom();
    }, 1000);
};

const handleKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
};

const scrollToBottom = (): void => {
    nextTick(() => {
        if (messagesContainer.value) {
            messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
        }
    });
};

// 监听消息变化自动滚动
watch(() => layoutStore.messages.length, scrollToBottom);

onMounted(() => {
    scrollToBottom();
});
</script>

<style scoped>
.chat-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: white;
    min-width: 0;
    /* 允许面板收缩 */
}

.chat-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.chat-info h3 {
    margin: 0 0 4px 0;
    font-size: 16px;
}

.chat-status {
    font-size: 12px;
    color: #52c41a;
}

.chat-actions {
    display: flex;
    gap: 8px;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

.message {
    display: flex;
    margin-bottom: 20px;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    margin: 0 12px;
}

.message-content {
    max-width: 70%;
}

.message-bubble {
    padding: 12px 16px;
    border-radius: 18px;
    background: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
    line-height: 1.4;
}

.message.user .message-bubble {
    background: #1890ff;
    color: white;
}

.message-time {
    font-size: 11px;
    color: #999;
    margin-top: 4px;
    text-align: center;
}

.chat-input-area {
    border-top: 1px solid #e0e0e0;
    padding: 16px 20px;
    flex-shrink: 0;
}

.input-container {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.input-container :deep(.el-textarea) {
    flex: 1;
}

.send-btn {
    height: 72px;
    width: 48px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-panel {
        width: 100%;
    }

    .message-content {
        max-width: 85%;
    }
}
</style>