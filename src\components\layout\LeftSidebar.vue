<!-- components/layout/LeftSidebar.vue -->
<template>
    <div class="left-sidebar">
        <!-- 顶部Logo -->
        <div class="sidebar-header">
            <div class="logo">
                <i class="icon-robot"></i>
            </div>
            <h2 class="app-title">AI助手</h2>
        </div>

        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li v-for="item in navItems" :key="item.id"
                    :class="['nav-item', { active: layoutStore.activeNav === item.id }]" @click="handleNavClick(item)">
                    <i :class="['nav-icon', item.icon]"></i>
                    <span class="nav-text">{{ item.name }}</span>
                    <span v-if="item.badge !== undefined" class="nav-badge">
                        {{ item.badge }}
                    </span>
                </li>
            </ul>
        </nav>

        <!-- 底部用户信息 -->
        <div class="sidebar-footer">
            <div class="user-info">
                <el-avatar :size="32" :src="user.avatar">
                    <i class="icon-user"></i>
                </el-avatar>
                <div class="user-details">
                    <span class="username">{{ user.name }}</span>
                    <span class="user-status">{{ user.status }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useLayoutStore } from '@/stores/layout';
import { ref, computed } from 'vue';
import type { NavItem } from '@/types/layout';

const layoutStore = useLayoutStore();

const navItems = ref<NavItem[]>([
    { id: 'chat', name: '对话', icon: 'icon-chat' },
    { id: 'history', name: '知识库', icon: 'icon-history', badge: computed(() => layoutStore.unreadCount).value },
    { id: 'templates', name: '会话', icon: 'icon-template' },
    { id: 'knowledge', name: '音乐', icon: 'icon-knowledge' },
    { id: 'settings', name: '视频', icon: 'icon-settings' }
]);

const user = ref({
    name: '用户昵称',
    avatar: '',
    status: '在线' as '在线' | '忙碌' | '离开'
});

const handleNavClick = (item: NavItem): void => {
    layoutStore.setActiveNav(item.id);
};
</script>

<style scoped>
.left-sidebar {
    width: 120px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    display: flex;
    flex-direction: column;
    height: 100vh;
    flex-shrink: 0;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    font-size: 32px;
    margin-bottom: 10px;
}

.app-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
    background: rgba(52, 152, 219, 0.2);
    border-right: 3px solid #3498db;
}

.nav-icon {
    margin-right: 12px;
    font-size: 16px;
}

.nav-text {
    flex: 1;
    font-size: 14px;
}

.nav-badge {
    background: #e74c3c;
    color: white;
    border-radius: 10px;
    padding: 2px 8px;
    font-size: 12px;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-details {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.username {
    font-size: 14px;
    font-weight: 600;
}

.user-status {
    font-size: 12px;
    opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .left-sidebar {
        width: 60px;
    }

    .app-title,
    .nav-text,
    .user-details {
        display: none;
    }

    .nav-item {
        justify-content: center;
        padding: 15px;
    }

    .nav-icon {
        margin-right: 0;
        font-size: 20px;
    }
}
</style>