<!-- components/layout/RightContent.vue -->
<template>
    <div class="right-content">
        <!-- 智能对话页面 -->
        <template v-if="layoutStore.activeNav === 'chat'">
            <div class="chat-view">
                <!-- 左侧聊天列表 -->
                <div class="chat-sidebar">
                    <div class="chat-sidebar-header">
                        <h3>对话列表</h3>
                        <el-button type="primary" class="new-chat-btn" @click="handleNewChat">
                            <i class="icon-plus"></i>
                            &nbsp;&nbsp;&nbsp;新建对话
                        </el-button>
                    </div>
                    <ChatManagement />
                </div>
                <!-- 右侧聊天面板 -->
                <div class="chat-main">
                    <ChatPanel />
                </div>
            </div>
        </template>

        <!-- 历史记录页面 -->
        <template v-else-if="layoutStore.activeNav === 'history'">
            <div class="history-view">
                <div class="page-header">
                    <h2>历史记录</h2>
                    <p>查看所有的对话历史记录</p>
                </div>
                <div class="history-content">
                    <div class="history-stats">
                        <div class="stat-card">
                            <div class="stat-number">{{ layoutStore.chatHistory.length }}</div>
                            <div class="stat-label">总对话数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{{ layoutStore.messages.length }}</div>
                            <div class="stat-label">消息总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{{ layoutStore.unreadCount }}</div>
                            <div class="stat-label">未读对话</div>
                        </div>
                    </div>
                    <div class="history-list">
                        <div v-for="chat in layoutStore.chatHistory" :key="chat.id" class="history-item">
                            <div class="history-title">{{ chat.title }}</div>
                            <div class="history-preview">{{ chat.preview }}</div>
                            <div class="history-time">{{ chat.time }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <!-- 对话模板页面 -->
        <template v-else-if="layoutStore.activeNav === 'templates'">
            <div class="templates-view">
                <div class="page-header">
                    <h2>对话模板</h2>
                    <p>使用预设模板快速开始对话</p>
                </div>
                <div class="templates-grid">
                    <div v-for="template in templates" :key="template.id" class="template-card">
                        <div class="template-icon">
                            <i :class="template.icon"></i>
                        </div>
                        <div class="template-title">{{ template.title }}</div>
                        <div class="template-description">{{ template.description }}</div>
                        <el-button class="template-use-btn" @click="useTemplate(template)">使用模板</el-button>
                    </div>
                </div>
            </div>
        </template>

        <!-- 知识库页面 -->
        <template v-else-if="layoutStore.activeNav === 'knowledge'">
            <div class="knowledge-view">
                <div class="page-header">
                    <h2>知识库</h2>
                    <p>管理和查看AI知识库内容</p>
                </div>
                <div class="knowledge-content">
                    <div class="knowledge-categories">
                        <div class="category-card">
                            <i class="icon-book"></i>
                            <h4>技术文档</h4>
                            <p>编程、开发相关知识</p>
                        </div>
                        <div class="category-card">
                            <i class="icon-lightbulb"></i>
                            <h4>创意灵感</h4>
                            <p>创作、设计相关内容</p>
                        </div>
                        <div class="category-card">
                            <i class="icon-chart"></i>
                            <h4>数据分析</h4>
                            <p>数据处理、分析方法</p>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <!-- 系统设置页面 -->
        <template v-else-if="layoutStore.activeNav === 'settings'">
            <div class="settings-view">
                <div class="page-header">
                    <h2>系统设置</h2>
                    <p>个性化配置您的AI助手</p>
                </div>
                <div class="settings-content">
                    <div class="settings-section">
                        <h4>基础设置</h4>
                        <div class="setting-item">
                            <label>主题模式</label>
                            <select>
                                <option>浅色模式</option>
                                <option>深色模式</option>
                                <option>跟随系统</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label>语言设置</label>
                            <select>
                                <option>简体中文</option>
                                <option>English</option>
                            </select>
                        </div>
                    </div>
                    <div class="settings-section">
                        <h4>对话设置</h4>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" checked>
                                启用消息提示音
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" checked>
                                自动保存对话记录
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <!-- 默认页面 -->
        <template v-else>
            <div class="default-view">
                <div class="empty-state">
                    <i class="icon-feature"></i>
                    <h3>功能开发中</h3>
                    <p>该功能正在开发中，敬请期待</p>
                </div>
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
import { useLayoutStore } from '@/stores/layout';
import { ref } from 'vue';
import ChatManagement from './ChatManagement.vue';
import ChatPanel from './ChatPanel.vue';

const layoutStore = useLayoutStore();

// 对话模板数据
const templates = ref([
    {
        id: 1,
        title: '代码助手',
        description: '帮助您解决编程问题和代码优化',
        icon: 'icon-code'
    },
    {
        id: 2,
        title: '写作助手',
        description: '协助您进行文章写作和内容创作',
        icon: 'icon-edit'
    },
    {
        id: 3,
        title: '学习助手',
        description: '解答学习问题，提供知识讲解',
        icon: 'icon-study'
    },
    {
        id: 4,
        title: '翻译助手',
        description: '提供多语言翻译服务',
        icon: 'icon-translate'
    }
]);

const handleNewChat = (): void => {
    const newChat = {
        title: '新对话',
        preview: '开始新的对话...',
        time: new Date().toLocaleString('zh-CN')
    };
    layoutStore.addChatHistory(newChat);
};

const useTemplate = (template: any): void => {
    const newChat = {
        title: template.title,
        preview: template.description,
        time: new Date().toLocaleString('zh-CN')
    };
    layoutStore.addChatHistory(newChat);
    layoutStore.setActiveNav('chat');
};
</script>

<style scoped>
.right-content {
    flex: 1;
    height: 100vh;
    background: white;
    overflow: hidden;
}

/* 智能对话视图 */
.chat-view {
    display: flex;
    height: 100%;
}

.chat-sidebar {
    width: 320px;
    border-right: 1px solid #e0e0e0;
    background: #fafafa;
    display: flex;
    flex-direction: column;
}

.chat-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-sidebar-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.new-chat-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.new-chat-btn:hover {
    background: #40a9ff;
}

.chat-main {
    flex: 1;
    min-width: 0;
}

/* 页面头部通用样式 */
.page-header {
    padding: 30px 40px;
    border-bottom: 1px solid #e0e0e0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.page-header h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
}

.page-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

/* 历史记录视图 */
.history-view {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.history-content {
    flex: 1;
    padding: 30px 40px;
    overflow-y: auto;
}

.history-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #1890ff;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

.history-list {
    display: grid;
    gap: 12px;
}

.history-item {
    background: white;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.history-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.history-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.history-preview {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    line-height: 1.4;
}

.history-time {
    font-size: 12px;
    color: #999;
}

/* 对话模板视图 */
.templates-view {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    padding: 30px 40px;
    overflow-y: auto;
}

.template-card {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.template-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.template-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.template-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.template-description {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 20px;
}

.template-use-btn {
    padding: 10px 20px;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.template-use-btn:hover {
    background: #40a9ff;
}

/* 知识库视图 */
.knowledge-view {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.knowledge-content {
    flex: 1;
    padding: 30px 40px;
    overflow-y: auto;
}

.knowledge-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.category-card {
    background: white;
    padding: 32px 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.category-card i {
    font-size: 48px;
    color: #1890ff;
    margin-bottom: 16px;
}

.category-card h4 {
    margin: 0 0 12px 0;
    font-size: 18px;
    color: #333;
}

.category-card p {
    margin: 0;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

/* 系统设置视图 */
.settings-view {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.settings-content {
    flex: 1;
    padding: 30px 40px;
    overflow-y: auto;
}

.settings-section {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
}

.settings-section h4 {
    margin: 0 0 20px 0;
    font-size: 16px;
    color: #333;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 12px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.setting-item select {
    padding: 6px 12px;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    font-size: 14px;
}

.setting-item input[type="checkbox"] {
    margin-right: 8px;
}

/* 默认视图 */
.default-view {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-state {
    text-align: center;
    color: #999;
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .chat-sidebar {
        width: 280px;
    }

    .templates-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 20px;
        padding: 20px;
    }

    .knowledge-categories {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .chat-view {
        flex-direction: column;
    }

    .chat-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
    }

    .page-header {
        padding: 20px;
    }

    .history-content,
    .knowledge-content,
    .settings-content {
        padding: 20px;
    }

    .templates-grid {
        grid-template-columns: 1fr;
        padding: 20px;
    }

    .history-stats {
        grid-template-columns: 1fr;
    }
}
</style>