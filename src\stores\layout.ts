// stores/layout.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { LayoutState, NavItem, ChatHistory, Message } from '@/types/layout'

//1、定义store
export const useLayoutStore = defineStore('layout', () => {
  // 2、响应式状态
  const activeNav = ref<string>('chat')
  const chatHistory = ref<ChatHistory[]>([])
  const currentChat = ref<ChatHistory>()
  const messages = ref<Message[]>([])

  // 计算属性
  const unreadCount = computed(() => chatHistory.value.filter((chat) => chat.unread).length)

  const setActiveNav = (navId: string): void => {
    activeNav.value = navId
  }

  const addChatHistory = (chat: Omit<ChatHistory, 'id'>): void => {
    const newChat: ChatHistory = {
      id: Date.now(),
      ...chat,
    }
    chatHistory.value.unshift(newChat)
    setCurrentChat(newChat)
  }

  const setCurrentChat = (chat: ChatHistory): void => {
    currentChat.value = chat
    // 标记为已读
    chat.unread = false
    // 加载聊天记录的逻辑可以在这里实现
    loadChatMessages(chat.id)
  }

  const addMessage = (message: Omit<Message, 'id'>): void => {
    const newMessage: Message = {
      id: Date.now(),
      ...message,
    }
    messages.value.push(newMessage)
  }

  const loadChatMessages = (_chatId: number): void => {
    // 模拟加载消息
    messages.value = [
      {
        id: 1,
        type: 'assistant',
        content: '您好！我是AI助手，有什么可以帮助您的吗？',
        time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      },
    ]
  }

  const clearCurrentChat = (): void => {
    messages.value = []
    currentChat.value = undefined
  }

  return {
    // States
    activeNav,
    chatHistory,
    currentChat,
    messages,

    // Getters
    unreadCount,

    // Actions
    setActiveNav,
    addChatHistory,
    setCurrentChat,
    addMessage,
    clearCurrentChat,
  }
})
