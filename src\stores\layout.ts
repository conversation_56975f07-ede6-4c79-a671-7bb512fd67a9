/*
 * @Author: tianrui.li <EMAIL>
 * @Date: 2025-09-25 16:06:32
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2025-09-26 16:18:41
 * @FilePath: \ai-dialogue\src\stores\layout.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// stores/layout.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { LayoutState, NavItem, ChatHistory, Message } from '@/types/layout'

//1、定义store
export const useLayoutStore = defineStore('layout', () => {
  // 2、响应式状态
  const activeNav = ref<string>('chat')
  const chatHistory = ref<ChatHistory[]>([])
  const currentChat = ref<ChatHistory>()
  const messages = ref<Message[]>([
    {
      id: 1,
      type: 'assistant',
      content: '您好！我是AI助手，有什么可以帮助您的吗？',
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    },
  ])
  const isChatSidebarVisible = ref<boolean>(true)

  // 计算属性
  const unreadCount = computed(() => chatHistory.value.filter((chat) => chat.unread).length)

  const setActiveNav = (navId: string): void => {
    activeNav.value = navId
  }

  const toggleChatSidebar = (): void => {
    isChatSidebarVisible.value = !isChatSidebarVisible.value
  }

  const addChatHistory = (chat: Omit<ChatHistory, 'id'>): void => {
    const newChat: ChatHistory = {
      id: Date.now(),
      ...chat,
    }
    chatHistory.value.unshift(newChat)
    setCurrentChat(newChat)
  }

  const setCurrentChat = (chat: ChatHistory): void => {
    currentChat.value = chat
    // 标记为已读
    chat.unread = false
    // 加载聊天记录的逻辑可以在这里实现
    loadChatMessages(chat.id)
  }

  const addMessage = (message: Omit<Message, 'id'>): void => {
    const newMessage: Message = {
      id: Date.now(),
      ...message,
    }
    messages.value.push(newMessage)
  }

  const loadChatMessages = (_chatId: number): void => {
    // 模拟加载消息 - 这里可以根据chatId加载不同的消息记录
    // 对于新创建的聊天，始终显示欢迎消息
    messages.value = [
      {
        id: 1,
        type: 'assistant',
        content: '您好！我是AI助手，有什么可以帮助您的吗？',
        time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      },
    ]
  }

  const clearCurrentChat = (): void => {
    // 清空当前聊天，恢复初始欢迎消息
    messages.value = [
      {
        id: 1,
        type: 'assistant',
        content: '您好！我是AI助手，有什么可以帮助您的吗？',
        time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      },
    ]
    currentChat.value = undefined
  }

  return {
    // States
    activeNav,
    chatHistory,
    currentChat,
    messages,
    isChatSidebarVisible,

    // Getters
    unreadCount,

    // Actions
    setActiveNav,
    toggleChatSidebar,
    addChatHistory,
    setCurrentChat,
    addMessage,
    clearCurrentChat,
  }
})
