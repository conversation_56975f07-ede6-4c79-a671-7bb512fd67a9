export interface NavItem {
  id: string
  name: string
  icon: string
  badge?: number
}

export interface ChatHistory {
  id: number
  title: string
  time: string
  preview: string
  unread?: boolean
}

export interface Message {
  id: number
  type: 'user' | 'assistant'
  content: string
  time: string
  avatar?: string
}

export interface LayoutState {
  activeNav: string
  chatHistory: ChatHistory[]
  currentChat?: ChatHistory
}
