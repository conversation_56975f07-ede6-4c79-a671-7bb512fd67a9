<template>
    <div class="chat-layout">
        <LeftSidebar />
        <RightContent />
    </div>
</template>

<script setup lang="ts">
import { useLayoutStore } from '@/stores/layout';
import LeftSidebar from '@/components/layout/LeftSidebar.vue';
import RightContent from '@/components/layout/RightContent.vue';

const layoutStore = useLayoutStore();
</script>

<style scoped>
.chat-layout {
    display: flex;
    height: 100vh;
    background-color: #f5f5f5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-layout {
        flex-direction: column;
    }
}
</style>