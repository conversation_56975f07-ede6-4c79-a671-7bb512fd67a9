<template>
    <div class="chat-layout" :class="layoutStore.layoutClasses">
        <LeftSidebar />
        <CenterPanel v-if="layoutStore.isCenterVisible" />
        <ToggleButton :is-expanded="layoutStore.isCenterVisible" @toggle="layoutStore.toggleCenterPanel" />
        <ChatPanel />
    </div>
</template>

<script setup lang="ts">
import { useLayoutStore } from '@/stores/layout';
import LeftSidebar from '@/components/layout/LeftSidebar.vue';
import CenterPanel from '@/components/layout/CenterPanel.vue';
import ChatPanel from '@/components/layout/ChatPanel.vue';
import ToggleButton from '@/components/common/ToggleButton.vue';

const layoutStore = useLayoutStore();
</script>

<style scoped>
.chat-layout {
    display: flex;
    height: 100vh;
    background-color: #f5f5f5;
    transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-layout {
        position: relative;
    }
}
</style>