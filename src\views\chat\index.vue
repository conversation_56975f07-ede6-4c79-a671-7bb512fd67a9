<script setup lang="ts">
import { ref, computed } from 'vue'

// 响应式数据
const activeNavItem = ref('chat') // 当前激活的导航项
const isMiddlePanelVisible = ref(true) // 中间面板是否可见
const chatHistory = ref([
    { id: 1, title: '关于Vue3的问题', time: '2024-01-15 10:30' },
    { id: 2, title: 'JavaScript异步编程', time: '2024-01-14 15:20' },
    { id: 3, title: 'CSS布局技巧', time: '2024-01-13 09:45' },
])

// 导航菜单项
const navItems = [
    { key: 'chat', label: '聊天', icon: '💬' },
    { key: 'history', label: '历史记录', icon: '📝' },
    { key: 'settings', label: '设置', icon: '⚙️' },
    { key: 'profile', label: '个人资料', icon: '👤' },
]

// 计算属性
const rightPanelClass = computed(() => {
    return isMiddlePanelVisible.value ? 'right-panel' : 'right-panel expanded'
})

// 方法
const switchNav = (navKey: string) => {
    activeNavItem.value = navKey
}

const toggleMiddlePanel = () => {
    isMiddlePanelVisible.value = !isMiddlePanelVisible.value
}

const startNewChat = () => {
    console.log('开始新对话')
}

const selectChat = (chatId: number) => {
    console.log('选择对话:', chatId)
}
</script>

<template>
    <div class="chat-container">
        <!-- 左侧导航栏 -->
        <div class="left-sidebar">
            <div class="nav-header">
                <h2>AI助手</h2>
            </div>
            <nav class="nav-menu">
                <div v-for="item in navItems" :key="item.key" class="nav-item"
                    :class="{ active: activeNavItem === item.key }" @click="switchNav(item.key)">
                    <span class="nav-icon">{{ item.icon }}</span>
                    <span class="nav-label">{{ item.label }}</span>
                </div>
            </nav>
        </div>

        <!-- 中间内容区域 -->
        <div class="middle-panel" v-show="isMiddlePanelVisible">
            <!-- 新建聊天页面 -->
            <div v-if="activeNavItem === 'chat'" class="content-section">
                <div class="section-header">
                    <h3>开始新对话</h3>
                </div>
                <div class="new-chat-area">
                    <div class="welcome-message">
                        <h4>👋 你好！我是你的AI助手</h4>
                        <p>我可以帮助你解答问题、提供建议或进行有趣的对话。请告诉我你想聊什么？</p>
                    </div>
                    <button class="new-chat-btn" @click="startNewChat">
                        <span>💬</span>
                        开始新对话
                    </button>
                </div>
            </div>

            <!-- 历史记录页面 -->
            <div v-else-if="activeNavItem === 'history'" class="content-section">
                <div class="section-header">
                    <h3>聊天历史</h3>
                </div>
                <div class="history-list">
                    <div v-for="chat in chatHistory" :key="chat.id" class="history-item" @click="selectChat(chat.id)">
                        <div class="history-title">{{ chat.title }}</div>
                        <div class="history-time">{{ chat.time }}</div>
                    </div>
                </div>
            </div>

            <!-- 设置页面 -->
            <div v-else-if="activeNavItem === 'settings'" class="content-section">
                <div class="section-header">
                    <h3>设置</h3>
                </div>
                <div class="settings-content">
                    <div class="setting-item">
                        <label>主题模式</label>
                        <select>
                            <option>浅色模式</option>
                            <option>深色模式</option>
                            <option>跟随系统</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>语言设置</label>
                        <select>
                            <option>中文</option>
                            <option>English</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 个人资料页面 -->
            <div v-else-if="activeNavItem === 'profile'" class="content-section">
                <div class="section-header">
                    <h3>个人资料</h3>
                </div>
                <div class="profile-content">
                    <div class="profile-avatar">
                        <div class="avatar-circle">👤</div>
                    </div>
                    <div class="profile-info">
                        <h4>用户名</h4>
                        <p><EMAIL></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧聊天区域 -->
        <div :class="rightPanelClass">
            <!-- 收缩按钮 -->
            <button class="toggle-btn" @click="toggleMiddlePanel">
                <span v-if="isMiddlePanelVisible">◀</span>
                <span v-else>▶</span>
            </button>

            <!-- 聊天内容 -->
            <div class="chat-content">
                <div class="chat-header">
                    <h3>AI对话</h3>
                </div>

                <div class="messages-container">
                    <div class="message user-message">
                        <div class="message-content">
                            你好，我想了解一下Vue3的新特性
                        </div>
                        <div class="message-time">14:30</div>
                    </div>

                    <div class="message ai-message">
                        <div class="message-content">
                            你好！很高兴为你介绍Vue3的新特性。Vue3相比Vue2有很多重要的改进：

                            1. **Composition API** - 提供了更灵活的组件逻辑组织方式
                            2. **更好的TypeScript支持** - 原生TypeScript支持
                            3. **性能提升** - 更小的包体积，更快的渲染速度
                            4. **Fragment支持** - 组件可以有多个根节点

                            你想了解哪个方面的详细信息呢？
                        </div>
                        <div class="message-time">14:31</div>
                    </div>
                </div>

                <div class="input-area">
                    <div class="input-container">
                        <textarea placeholder="输入你的消息..." rows="3"></textarea>
                        <button class="send-btn">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* 全局容器 */
.chat-container {
    display: flex;
    height: 100vh;
    background-color: #f5f5f5;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 左侧导航栏 */
.left-sidebar {
    width: 240px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.nav-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.nav-menu {
    flex: 1;
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
    background-color: rgba(255, 255, 255, 0.15);
    border-left-color: #fff;
}

.nav-icon {
    font-size: 18px;
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

.nav-label {
    font-size: 14px;
    font-weight: 500;
}

/* 中间面板 */
.middle-panel {
    width: 320px;
    background-color: white;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.content-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.section-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #fafafa;
}

.section-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
    font-weight: 600;
}

/* 新建聊天区域 */
.new-chat-area {
    flex: 1;
    padding: 30px 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.welcome-message {
    margin-bottom: 30px;
}

.welcome-message h4 {
    margin: 0 0 15px 0;
    font-size: 20px;
    color: #333;
}

.welcome-message p {
    margin: 0;
    color: #666;
    line-height: 1.6;
}

.new-chat-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.new-chat-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 历史记录列表 */
.history-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.history-item {
    padding: 15px;
    margin-bottom: 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.history-item:hover {
    background-color: #f0f0f0;
    border-left-color: #667eea;
}

.history-title {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
    font-size: 14px;
}

.history-time {
    font-size: 12px;
    color: #999;
}

/* 设置页面 */
.settings-content {
    flex: 1;
    padding: 20px;
}

.setting-item {
    margin-bottom: 20px;
}

.setting-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.setting-item select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

/* 个人资料页面 */
.profile-content {
    flex: 1;
    padding: 30px 20px;
    text-align: center;
}

.profile-avatar {
    margin-bottom: 20px;
}

.avatar-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: white;
    margin: 0 auto;
}

.profile-info h4 {
    margin: 0 0 8px 0;
    color: #333;
}

.profile-info p {
    margin: 0;
    color: #666;
}

/* 右侧聊天区域 */
.right-panel {
    flex: 1;
    background-color: white;
    display: flex;
    flex-direction: column;
    position: relative;
    transition: all 0.3s ease;
}

.right-panel.expanded {
    margin-left: 0;
}

.toggle-btn {
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    z-index: 20;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.toggle-btn:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-50%) scale(1.05);
}

.chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #fafafa;
}

.chat-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
    font-weight: 600;
}

/* 消息容器 */
.messages-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: linear-gradient(to bottom, #f8f9fa 0%, #ffffff 100%);
}

.message {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
}

.user-message {
    align-items: flex-end;
}

.ai-message {
    align-items: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.user-message .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 6px;
}

.ai-message .message-content {
    background-color: #f1f3f4;
    color: #333;
    border-bottom-left-radius: 6px;
    border: 1px solid #e0e0e0;
}

.message-time {
    font-size: 11px;
    color: #999;
    margin-top: 5px;
    padding: 0 16px;
}

/* 输入区域 */
.input-area {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background-color: white;
}

.input-container {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.input-container textarea {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 20px;
    resize: none;
    font-size: 14px;
    font-family: inherit;
    outline: none;
    transition: all 0.3s ease;
    max-height: 120px;
}

.input-container textarea:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.send-btn {
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.send-btn:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-container {
        flex-direction: column;
    }

    .left-sidebar {
        width: 100%;
        height: auto;
    }

    .middle-panel {
        width: 100%;
    }

    .right-panel {
        width: 100%;
    }

    .toggle-btn {
        display: none;
    }
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar,
.history-list::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track,
.history-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb,
.history-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover,
.history-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
